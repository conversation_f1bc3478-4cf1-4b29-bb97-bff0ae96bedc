/**
 * CF7TextEditor Class - Font Management Methods
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorFontManagement {
    // ========================================
    // FONT MANAGEMENT METHODS
    // ========================================

    updateSelectedFont(property, value) {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        // Apply font styles to the editable content area
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        if (editableContent) {
            editableContent.style[property] = value;
        } else {
            // Fallback for older elements
            this.selectedElement.style[property] = value;
        }
    }

    toggleSelectedFont(property, activeValue, inactiveValue) {
        // Get the button that was clicked for visual feedback
        let button = null;
        if (property === 'fontWeight') button = this.fontControls.bold;
        if (property === 'fontStyle') button = this.fontControls.italic;

        // If no element is selected, just toggle button visual state
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
            if (button) {
                button.classList.toggle('cf7-active');
                console.log('No element selected, toggled button state:', button.classList.contains('cf7-active'));
            }
            return;
        }

        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        const currentValue = targetElement.style[property] ||
                           window.getComputedStyle(targetElement)[property];
        const newValue = (currentValue === activeValue) ? inactiveValue : activeValue;

        console.log(`Toggling ${property}: ${currentValue} -> ${newValue}`);
        targetElement.style[property] = newValue;

        // Don't call updateFontControlsFromElement as it might override our button state
        // Instead, manually update the button state
        if (button) {
            const isActive = (newValue === activeValue);
            button.classList.toggle('cf7-active', isActive);
            console.log(`Button ${property} active state:`, isActive);
        }
    }

        // CF7 Compatible: Set text alignment for selected text element
        setTextAlignment(alignment) {
            console.log('setTextAlignment called with:', alignment);
            console.log('Selected element:', this.selectedElement);

            // Get the alignment buttons for visual feedback
            const alignButtons = {
                left: this.fontControls.alignLeft,
                center: this.fontControls.alignCenter,
                right: this.fontControls.alignRight,
                justify: this.fontControls.alignJustify
            };

            console.log('Alignment buttons:', alignButtons);

            // If no element is selected, just update button visual state
            if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
                // Clear all alignment button active states
                Object.values(alignButtons).forEach(btn => {
                    if (btn) btn.classList.remove('cf7-active');
                });
                // Set the clicked button as active
                if (alignButtons[alignment]) {
                    alignButtons[alignment].classList.add('cf7-active');
                }
                return;
            }

            const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
            const targetElement = editableContent || this.selectedElement;

            // Apply text alignment with !important to override any conflicting styles
            targetElement.style.setProperty('text-align', alignment, 'important');
            console.log(`Text alignment set to: ${alignment}`);

            // For justify, add additional properties
            if (alignment === 'justify') {
                targetElement.style.setProperty('text-align-last', 'left', 'important');
                targetElement.style.setProperty('word-spacing', 'normal', 'important');
                targetElement.style.setProperty('letter-spacing', 'normal', 'important');
                console.log('Justify alignment applied with additional properties');
            } else {
                // Remove justify-specific properties for other alignments
                targetElement.style.removeProperty('text-align-last');
            }

            console.log('Element computed textAlign:', window.getComputedStyle(targetElement).textAlign);
            console.log('Element style textAlign:', targetElement.style.textAlign);

            // Update button visual states - only one alignment can be active at a time
            console.log('Updating button states for alignment:', alignment);
            Object.entries(alignButtons).forEach(([align, btn]) => {
                if (btn) {
                    const shouldBeActive = align === alignment;
                    console.log(`Button ${align}: ${shouldBeActive ? 'ACTIVE' : 'inactive'}`);
                    if (shouldBeActive) {
                        btn.classList.add('cf7-active');
                    } else {
                        btn.classList.remove('cf7-active');
                    }
                }
            });
        }

        // Debug method to test button toggle
        testBoldButtonToggle() {
            if (this.fontControls.bold) {
                console.log('Testing Bold button toggle...');
                console.log('Before toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
                this.fontControls.bold.classList.toggle('cf7-active');
                console.log('After toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
                console.log('Button classes:', this.fontControls.bold.className);
            } else {
                console.log('Bold button not found!');
            }
        }

        // Text Shadow Methods - Following CF7 patterns
        toggleTextShadow() {
            if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

            const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
            if (!shadowToggle) return;

            const isActive = shadowToggle.classList.contains('cf7-active');
            const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
            const targetElement = editableContent || this.selectedElement;

            if (isActive) {
                // Remove text shadow
                targetElement.style.textShadow = 'none';
                this.selectedElement.classList.remove('cf7-has-shadow');
                shadowToggle.classList.remove('cf7-active');

                // Mark shadow as not applied
                if (this.selectedElement.shadowConfig) {
                    this.selectedElement.shadowConfig.applied = false;
                }
            } else {
                // Apply text shadow with current settings
                this.applyTextShadowToSelected();
                this.selectedElement.classList.add('cf7-has-shadow');
                shadowToggle.classList.add('cf7-active');
            }
        }

        updateTextShadow(property, value) {
            if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

            const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
            if (!shadowToggle || !shadowToggle.classList.contains('cf7-active')) return;

            // Store shadow properties on the element - CF7 Compatible
            if (!this.selectedElement.shadowConfig) {
                this.selectedElement.shadowConfig = {
                    color: '#000000',
                    blur: '2',
                    offsetX: '2',
                    offsetY: '2',
                    opacity: '100'
                };
            }

            this.selectedElement.shadowConfig[property] = value;
            this.applyTextShadowToSelected();
        }

        applyTextShadowToSelected() {
            if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

            const config = this.selectedElement.shadowConfig || {
                color: this.getShadowControlValue('color') || '#000000',
                blur: this.getShadowControlValue('blur') || '2',
                offsetX: this.getShadowControlValue('offsetX') || '2',
                offsetY: this.getShadowControlValue('offsetY') || '2',
                opacity: this.getShadowControlValue('opacity') || '100'
            };

            // Get current values from controls - CF7 Compatible
            const shadowColor = config.color || '#000000';
            const shadowBlur = config.blur || '2';
            const shadowOffsetX = config.offsetX || '2';
            const shadowOffsetY = config.offsetY || '2';
            const shadowOpacity = config.opacity || '100';

            // Convert hex color to rgba with opacity
            const rgba = this.hexToRgba(shadowColor, shadowOpacity / 100);

            // Apply text shadow using CSS text-shadow property with opacity
            const textShadow = `${shadowOffsetX}px ${shadowOffsetY}px ${shadowBlur}px ${rgba}`;

            // Apply to editable content if it exists, otherwise to the element
            const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
            if (editableContent) {
                editableContent.style.textShadow = textShadow;
            } else {
                this.selectedElement.style.textShadow = textShadow;
            }

            // Store the shadow config on the element for persistence
            this.selectedElement.shadowConfig = {
                ...config,
                applied: true
            };
        }

        getShadowControlValue(property) {
            const controls = {
                color: this.container.querySelector('.cf7-color-picker[data-shadow="color"]'),
                blur: this.container.querySelector('.cf7-input-shadow-blur'),
                offsetX: this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]'),
                offsetY: this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]'),
                opacity: this.container.querySelector('.cf7-range-slider[data-shadow-opacity="true"]')
            };

            const control = controls[property];
            if (!control) return null;

            return control.value || null;
        }

        // CF7 Helper: Convert hex color to rgba with opacity
        hexToRgba(hex, opacity = 1) {
            // Remove # if present
            hex = hex.replace('#', '');

            // Parse hex values
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);

            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }

        // Background Template Methods
        updateTemplateOptions(category) {
            console.log('Updating template options for category:', category);

            if (!category) {
                // Reset template dropdown
                if (this.backgroundControls.template) {
                    this.backgroundControls.template.innerHTML = '<option value="">Select Template...</option>';
                }
                return;
            }

            const templates = this.backgroundTemplates[category] || [];
            console.log('Templates found:', templates);

            const templateOptions = [
                { value: '', text: 'Select Template...' },
                ...templates.map(template => ({ value: template.url, text: template.name }))
            ];

            if (this.backgroundControls.template) {
                this.backgroundControls.template.innerHTML = '';
                templateOptions.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.text;
                    this.backgroundControls.template.appendChild(optionElement);
                });
                console.log('Template dropdown updated with', templateOptions.length, 'options');
            }
        }

        setBackgroundImage(imageUrl) {
            console.log('Setting background image:', imageUrl);
            const backgroundElement = document.getElementById('cf7-canvas-bg');
            console.log('Background element found:', backgroundElement);

            if (backgroundElement) {
                if (imageUrl) {
                    backgroundElement.style.backgroundImage = `url("${imageUrl}")`;
                    console.log('Background image set to:', backgroundElement.style.backgroundImage);
                } else {
                    backgroundElement.style.backgroundImage = '';
                    console.log('Background image cleared');
                }
            } else {
                console.error('Background element not found!');
            }
        }

    clearBackground() {
        this.setBackgroundImage('');
        if (this.modal) {
            this.modal.selectedCategory = null;
            this.modal.selectedTemplate = null;
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorFontManagement;
}