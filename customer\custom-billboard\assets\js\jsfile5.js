/**
 * CF7TextEditor Class - Shortcode Conversion Methods
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorShortcodes {
    // ========================================
    // SHORTCODE CONVERSION METHODS
    // ========================================

    convertShortcodeToButton(shortcode, className, callback) {
        const walker = document.createTreeWalker(
            this.container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const text = this.extractShortcodeAttribute(textNode.textContent, 'text') || 'Button';

                const button = document.createElement('button');
                button.textContent = text;
                button.className = className;
                button.type = 'button'; // Prevent form submission in WordPress

                // WordPress-compatible event handling
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    callback();
                });

                parent.appendChild(button);

                textNode.textContent = '';
                break;
            }
        }
    }

    convertShortcodeToSelect(shortcode, className, options, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const select = document.createElement('select');
                select.className = className;

                options.forEach(option => {
                    const optionEl = document.createElement('option');
                    optionEl.value = option.value;
                    optionEl.textContent = option.text;
                    select.appendChild(optionEl);
                });

                select.onchange = (e) => callback(e.target.value);
                parent.appendChild(select);

                // Track controls based on shortcode type
                if (shortcode.includes('font-family')) {
                    this.fontControls.fontFamily = select;
                } else if (shortcode.includes('font-size')) {
                    this.fontControls.fontSize = select;
                } else if (shortcode.includes('background-category')) {
                    this.backgroundControls.category = select;
                } else if (shortcode.includes('background-template')) {
                    this.backgroundControls.template = select;
                }

                textNode.textContent = '';
                break;
            }
        }
    }

    // CF7 Font Preview Dropdown - Shows each font in its own typeface
    createFontPreviewDropdown(shortcode, className, options, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;

                // Create custom dropdown container
                const dropdownContainer = document.createElement('div');
                dropdownContainer.className = 'cf7-font-preview-dropdown';

                // Create button that shows selected font
                const selectButton = document.createElement('button');
                selectButton.type = 'button';
                selectButton.className = 'cf7-font-preview-button';
                selectButton.innerHTML = `
                    <span class="cf7-selected-font">Select Font</span>
                    <span class="cf7-dropdown-arrow">▼</span>
                `;

                // Create dropdown list
                const dropdownList = document.createElement('ul');
                dropdownList.className = 'cf7-font-preview-list cf7-hidden';
                dropdownList.setAttribute('role', 'listbox');

                // Add options to dropdown
                options.forEach((option) => {
                    const listItem = document.createElement('li');
                    listItem.className = 'cf7-font-preview-option';
                    listItem.setAttribute('role', 'option');
                    listItem.setAttribute('data-value', option.value);
                    listItem.style.fontFamily = option.family || option.value;
                    listItem.textContent = option.text;

                    // Handle option selection
                    listItem.addEventListener('click', () => {
                        // Update button text and font
                        const selectedSpan = selectButton.querySelector('.cf7-selected-font');
                        selectedSpan.textContent = option.text;
                        selectedSpan.style.fontFamily = option.family || option.value;

                        // Remove selected class from all options
                        dropdownList.querySelectorAll('.cf7-font-preview-option').forEach(opt => {
                            opt.classList.remove('cf7-selected');
                        });

                        // Add selected class to current option
                        listItem.classList.add('cf7-selected');

                        // Close dropdown
                        dropdownList.classList.add('cf7-hidden');
                        selectButton.setAttribute('aria-expanded', 'false');

                        // Call callback
                        callback(option.value);
                    });

                    dropdownList.appendChild(listItem);
                });

                // Toggle dropdown functionality
                selectButton.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Don't open if disabled
                    if (dropdownContainer.classList.contains('cf7-disabled') || selectButton.disabled) {
                        return;
                    }

                    const isOpen = !dropdownList.classList.contains('cf7-hidden');

                    // Close all other font dropdowns first
                    document.querySelectorAll('.cf7-font-preview-list').forEach(list => {
                        if (list !== dropdownList) {
                            list.classList.add('cf7-hidden');
                        }
                    });

                    // Toggle current dropdown
                    dropdownList.classList.toggle('cf7-hidden');
                    selectButton.setAttribute('aria-expanded', !isOpen);
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!dropdownContainer.contains(e.target)) {
                        dropdownList.classList.add('cf7-hidden');
                        selectButton.setAttribute('aria-expanded', 'false');
                    }
                });

                // Assemble dropdown
                dropdownContainer.appendChild(selectButton);
                dropdownContainer.appendChild(dropdownList);
                parent.appendChild(dropdownContainer);

                // Track control
                if (shortcode.includes('font-family')) {
                    this.fontControls.fontFamily = dropdownContainer;
                }

                textNode.textContent = '';
                break;
            }
        }
    }

    convertShortcodeToInput(shortcode, className, type, defaultValue, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const input = document.createElement('input');
                input.type = type;
                input.className = className;
                input.value = defaultValue;

                // Set appropriate min/max based on input type
                if (className.includes('cf7-input-size')) {
                    input.min = '8';
                    input.max = '72';
                    this.fontControls.fontSize = input;
                } else if (className.includes('cf7-input-shadow-blur')) {
                    input.min = '0';
                    input.max = '20';
                    input.setAttribute('data-shadow-blur', 'true');
                } else if (className.includes('cf7-input-shadow-offset')) {
                    input.min = '-20';
                    input.max = '20';
                    if (shortcode.includes('offset-x')) {
                        input.setAttribute('data-axis', 'x');
                    } else if (shortcode.includes('offset-y')) {
                        input.setAttribute('data-axis', 'y');
                    }
                }

                input.oninput = (e) => callback(e.target.value);
                parent.appendChild(input);
                textNode.textContent = '';
                break;
            }
        }
    }

    convertShortcodeToToggle(shortcode, className, callback) {
        console.log('convertShortcodeToToggle called with shortcode:', shortcode);
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                console.log('Found shortcode in text:', textNode.textContent);
                const parent = textNode.parentElement;
                const text = this.extractShortcodeAttribute(textNode.textContent, 'text') || 'Toggle';

                const button = document.createElement('button');
                button.textContent = text;
                button.className = className;
                button.type = 'button'; // Prevent form submission in WordPress

                // WordPress-compatible event handling
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    callback();
                });

                parent.appendChild(button);

                if (text === 'B') {
                    this.fontControls.bold = button;
                    // Add direct test for Bold button
                    console.log('Bold button created:', button);
                    console.log('Bold button classes:', button.className);
                }
                if (text === 'I') this.fontControls.italic = button;
                if (text === 'S') this.fontControls.textShadow = button;

                // Text alignment buttons - check for Unicode symbols
                if (text === '⬅') {
                    this.fontControls.alignLeft = button;
                    console.log('Left align button created:', button);
                }
                if (text === '⬌') {
                    this.fontControls.alignCenter = button;
                    console.log('Center align button created:', button);
                }
                if (text === '➡') {
                    this.fontControls.alignRight = button;
                    console.log('Right align button created:', button);
                }
                if (text === '≡') {
                    this.fontControls.alignJustify = button;
                    console.log('Justify align button created:', button);
                }

                textNode.textContent = '';
                break;
            }
        }
    }

    convertShortcodeToColorPicker(shortcode, className, defaultValue, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const value = this.extractShortcodeAttribute(textNode.textContent, 'value') || defaultValue;

                const colorPicker = document.createElement('input');
                colorPicker.type = 'color';
                colorPicker.className = className;
                colorPicker.value = value;

                // Set appropriate title and data attributes based on shortcode
                if (shortcode.includes('cf7-font-color')) {
                    colorPicker.title = 'Choose font color';
                    this.fontControls.color = colorPicker;
                } else if (shortcode.includes('cf7-shadow-color')) {
                    colorPicker.title = 'Choose shadow color';
                    colorPicker.setAttribute('data-shadow', 'color');
                }

                colorPicker.onchange = (e) => callback(e.target.value);
                parent.appendChild(colorPicker);
                textNode.textContent = '';
                break;
            }
        }
    }

    // CF7 Range Slider Converter - Contact Form 7 Compatible
    convertShortcodeToRangeSlider(shortcode, className, min, max, defaultValue, callback, valueDisplayId, unit) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const value = this.extractShortcodeAttribute(textNode.textContent, 'value') || defaultValue;

                const rangeSlider = document.createElement('input');
                rangeSlider.type = 'range';
                rangeSlider.className = className;
                rangeSlider.min = min;
                rangeSlider.max = max;
                rangeSlider.value = value;

                // WordPress-specific fixes for range slider
                rangeSlider.setAttribute('min', min);
                rangeSlider.setAttribute('max', max);
                rangeSlider.setAttribute('value', value);
                rangeSlider.defaultValue = value;

                // Set data attributes for shadow control identification
                if (shortcode.includes('opacity')) {
                    rangeSlider.setAttribute('data-shadow-opacity', 'true');
                }

                // WordPress-compatible event handling for range slider
                rangeSlider.addEventListener('input', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const currentValue = e.target.value;
                    callback(currentValue);

                    // Update value display
                    const valueDisplay = document.getElementById(valueDisplayId);
                    if (valueDisplay) {
                        valueDisplay.textContent = currentValue + unit;
                    }
                });

                // Initialize value display
                const valueDisplay = document.getElementById(valueDisplayId);
                if (valueDisplay) {
                    valueDisplay.textContent = value + unit;
                }

                parent.appendChild(rangeSlider);

                // WordPress compatibility fix - Ensure correct value after DOM insertion
                setTimeout(() => {
                    rangeSlider.value = value;
                    rangeSlider.setAttribute('value', value);
                    if (valueDisplay) {
                        valueDisplay.textContent = value + unit;
                    }
                }, 10);

                textNode.textContent = '';
                break;
            }
        }
    }

    extractShortcodeAttribute(shortcode, attribute) {
        const regex = new RegExp(`${attribute}:"([^"]*)"`, 'i');
        const match = shortcode.match(regex);
        return match ? match[1] : null;
    }

    setupEventListeners() {
        // Canvas click to deselect elements
        this.canvas.addEventListener('click', (e) => {
            if (e.target === this.canvas || e.target === this.elementsContainer) {
                this.deselectAll();
            }
        });

        // Global mouse events for dragging and resizing
        document.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
            this.handleResizeMove(e);
        });
        document.addEventListener('mouseup', () => {
            this.handleMouseUp();
            this.handleResizeUp();
        });

        // Test button for alignment
        const testButton = document.getElementById('test-align-left');
        if (testButton) {
            testButton.addEventListener('click', () => {
                console.log('Test button clicked!');
                this.setTextAlignment('left');
            });
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorShortcodes;
}