/**
 * CF7TextEditor Class - Utility Functions and Initialization
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorUtilities {
    // ========================================
    // UTILITY FUNCTIONS AND INITIALIZATION
    // ========================================

    // CF7 Style Initialization - Following CF7 patterns
    static initializeEditors() {
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all CF7 text editors on the page
            const editors = document.querySelectorAll('.cf7-text-editor-container');
            editors.forEach(container => {
                const editor = new CF7TextEditor(container);
                // Add to window for debugging
                if (!window.cf7Editors) window.cf7Editors = [];
                window.cf7Editors.push(editor);
            });

            // Add global test function
            window.testBoldButton = function() {
                if (window.cf7Editors && window.cf7Editors[0]) {
                    window.cf7Editors[0].testBoldButtonToggle();
                } else {
                    console.log('No CF7 editors found');
                }
            };

            // Add test function for alignment buttons
            window.testAlignButtons = function() {
                if (window.cf7Editors && window.cf7Editors[0]) {
                    const editor = window.cf7Editors[0];
                    console.log('Font controls object:', editor.fontControls);
                    console.log('Align Left button:', editor.fontControls.alignLeft);
                    console.log('Align Center button:', editor.fontControls.alignCenter);
                    console.log('Align Right button:', editor.fontControls.alignRight);
                    console.log('Align Justify button:', editor.fontControls.alignJustify);
                } else {
                    console.log('No CF7 editors found');
                }
            };

            // Add test function to directly call setTextAlignment
            window.testSetAlignment = function(alignment) {
                if (window.cf7Editors && window.cf7Editors[0]) {
                    console.log('Calling setTextAlignment with:', alignment);
                    window.cf7Editors[0].setTextAlignment(alignment);
                } else {
                    console.log('No CF7 editors found');
                }
            };

            // Add test function specifically for justify
            window.testJustify = function() {
            if (window.cf7Editors && window.cf7Editors[0]) {
                const editor = window.cf7Editors[0];
                if (editor.selectedElement) {
                    const element = editor.selectedElement;
                    const editableContent = element.querySelector('.cf7-editable-content');
                    const targetElement = editableContent || element;

                    console.log('Testing justify on element:', targetElement);
                    console.log('Current text:', targetElement.textContent);
                    console.log('Element width:', targetElement.offsetWidth);
                    console.log('Element height:', targetElement.offsetHeight);

                    // Force justify with important
                    targetElement.style.setProperty('text-align', 'justify', 'important');
                    targetElement.style.setProperty('text-align-last', 'left', 'important');

                    console.log('After setting justify:');
                    console.log('Computed textAlign:', window.getComputedStyle(targetElement).textAlign);
                    console.log('Style textAlign:', targetElement.style.textAlign);
                } else {
                    console.log('No element selected');
                }
            } else {
                console.log('No CF7 editors found');
            }
        };
        });
    }
}

// Initialize editors when DOM is ready
CF7TextEditorUtilities.initializeEditors();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorUtilities;
}