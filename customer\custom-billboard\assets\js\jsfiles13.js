/**
 * CF7TextEditor Class - Global Function Exports
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorGlobals {
    // Make functions globally accessible
    static initializeGlobals() {
        window.openCheckoutDialog = window.openCheckoutDialog || function() {
            console.log('openCheckoutDialog function not yet loaded');
        };
        window.closeCheckoutDialog = window.closeCheckoutDialog || function() {
            console.log('closeCheckoutDialog function not yet loaded');
        };
        window.proceedToPayment = window.proceedToPayment || function() {
            console.log('proceedToPayment function not yet loaded');
        };
        window.openTermsModal = window.openTermsModal || function() {
            console.log('openTermsModal function not yet loaded');
        };
    }
}

// Initialize globals
CF7TextEditorGlobals.initializeGlobals();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorGlobals;
}