/**
 * CF7TextEditor Class - Font Control Setup
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorFonts {
    // ========================================
    // FONT CONTROL SETUP
    // ========================================

    setupFontControls() {
        // Enhanced Font family dropdown with Google Fonts - CF7 Compatible with Font Preview
        this.createFontPreviewDropdown('[cf7-font-family', 'cf7-select-font', [
            // Sans-serif fonts (most readable for digital)
            { value: 'Inter, Arial, sans-serif', text: 'Inter (Modern)', family: 'Inter' },
            { value: 'Roboto, Arial, sans-serif', text: 'Roboto (Clean)', family: 'Roboto' },
            { value: 'Open Sans, Arial, sans-serif', text: 'Open Sans (Friendly)', family: 'Open Sans' },
            { value: 'Lato, Arial, sans-serif', text: 'Lato (Professional)', family: 'Lato' },
            { value: 'Montserrat, Arial, sans-serif', text: 'Montserrat (Geometric)', family: 'Montserrat' },
            { value: 'Poppins, Arial, sans-serif', text: 'Poppins (Rounded)', family: 'Poppins' },
            { value: 'Source Sans Pro, Arial, sans-serif', text: 'Source Sans Pro', family: 'Source Sans Pro' },
            { value: 'Nunito, Arial, sans-serif', text: 'Nunito (Soft)', family: 'Nunito' },
            { value: 'Raleway, Arial, sans-serif', text: 'Raleway (Elegant)', family: 'Raleway' },

            // Serif fonts (traditional, formal)
            { value: 'Playfair Display, Georgia, serif', text: 'Playfair Display (Elegant)', family: 'Playfair Display' },
            { value: 'Merriweather, Georgia, serif', text: 'Merriweather (Readable)', family: 'Merriweather' },
            { value: 'Georgia, serif', text: 'Georgia (Classic)', family: 'Georgia' },
            { value: 'Times New Roman, serif', text: 'Times New Roman', family: 'Times New Roman' },

            // Display fonts (headlines, impact)
            { value: 'Oswald, Impact, sans-serif', text: 'Oswald (Bold Headlines)', family: 'Oswald' },
            { value: 'Impact, sans-serif', text: 'Impact (Strong)', family: 'Impact' },

            // Script/Decorative fonts (special occasions)
            { value: 'Dancing Script, cursive', text: 'Dancing Script (Handwritten)', family: 'Dancing Script' },
            { value: 'Pacifico, cursive', text: 'Pacifico (Playful)', family: 'Pacifico' },
            { value: 'Lobster, cursive', text: 'Lobster (Retro)', family: 'Lobster' },

            // System fallbacks
            { value: 'Arial, sans-serif', text: 'Arial (System)', family: 'Arial' },
            { value: 'Helvetica, sans-serif', text: 'Helvetica (System)', family: 'Helvetica' },
            { value: 'Verdana, sans-serif', text: 'Verdana (System)', family: 'Verdana' },
            { value: 'Courier New, monospace', text: 'Courier New (Monospace)', family: 'Courier New' }
        ], (value) => this.updateSelectedFont('fontFamily', value));

        // Font size input
        this.convertShortcodeToInput('[cf7-font-size', 'cf7-input-size', 'number', '16',
            (value) => this.updateSelectedFont('fontSize', value + 'px'));

        // Font style buttons
        this.convertShortcodeToToggle('[cf7-font-bold', 'cf7-btn-style',
            () => this.toggleSelectedFont('fontWeight', 'bold', 'normal'));
        this.convertShortcodeToToggle('[cf7-font-italic', 'cf7-btn-style',
            () => this.toggleSelectedFont('fontStyle', 'italic', 'normal'));

        // Text alignment buttons
        console.log('Setting up text alignment buttons...');
        this.convertShortcodeToToggle('[cf7-align-left', 'cf7-btn-style',
            () => {
                console.log('Left align clicked');
                this.setTextAlignment('left');
            });
        this.convertShortcodeToToggle('[cf7-align-center', 'cf7-btn-style',
            () => {
                console.log('Center align clicked');
                this.setTextAlignment('center');
            });
        this.convertShortcodeToToggle('[cf7-align-right', 'cf7-btn-style',
            () => {
                console.log('Right align clicked');
                this.setTextAlignment('right');
            });
        this.convertShortcodeToToggle('[cf7-align-justify', 'cf7-btn-style',
            () => {
                console.log('Justify align clicked');
                this.setTextAlignment('justify');
            });

        // Font color picker
        this.convertShortcodeToColorPicker('[cf7-font-color', 'cf7-color-picker', '#000000',
            (value) => this.updateSelectedFont('color', value));

        // Text Shadow Controls
        this.convertShortcodeToToggle('[cf7-text-shadow-toggle', 'cf7-btn-shadow',
            () => this.toggleTextShadow());
        this.convertShortcodeToColorPicker('[cf7-shadow-color', 'cf7-color-picker', '#000000',
            (value) => this.updateTextShadow('color', value));
        this.convertShortcodeToInput('[cf7-shadow-blur', 'cf7-input-shadow-blur', 'number', '2',
            (value) => this.updateTextShadow('blur', value));
        this.convertShortcodeToInput('[cf7-shadow-offset-x', 'cf7-input-shadow-offset', 'number', '2',
            (value) => this.updateTextShadow('offsetX', value));
        this.convertShortcodeToInput('[cf7-shadow-offset-y', 'cf7-input-shadow-offset', 'number', '2',
            (value) => this.updateTextShadow('offsetY', value));

        // Text Shadow Opacity Slider - CF7 Compatible
        this.convertShortcodeToRangeSlider('[cf7-shadow-opacity-slider', 'cf7-range-slider', 0, 100, 100,
            (value) => this.updateTextShadow('opacity', value), 'cf7-opacity-value', '%');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorFonts;
}