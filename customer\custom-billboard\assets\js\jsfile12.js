/**
 * CF7TextEditor Class - Checkout Dialog Functions
 * Part of the Billboard Maker functionality
 */
class CF7TextEditorCheckout {
    // Checkout Dialog Functions
    static openCheckoutDialog() {
        console.log('Opening checkout dialog...');

        // Validate that we have the required data from the location form
        const requiredData = {
            purpose: localStorage.getItem('selectedPurpose') || 'custom',
            state: localStorage.getItem('selectedState'),
            city: localStorage.getItem('selectedCity'),
            location: localStorage.getItem('billboardLocation'),
            dates: localStorage.getItem('runDates')
        };

        console.log('Checkout data validation:', requiredData);

        // Check for missing required fields
        const missingFields = [];
        if (!requiredData.state) missingFields.push('State');
        if (!requiredData.city) missingFields.push('City');
        if (!requiredData.location) missingFields.push('Billboard Location');
        if (!requiredData.dates) missingFields.push('Run Dates');

        if (missingFields.length > 0) {
            alert(`Please complete the following required information first:\n\n• ${missingFields.join('\n• ')}\n\nPlease fill out the location and dates form above before proceeding to checkout.`);
            return;
        }

        // Capture the current canvas image with high quality
        try {
            const canvas = document.getElementById('cf7-canvas');
            if (canvas) {
                // Create a temporary canvas for high-quality export
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');

                // Set high resolution (2x for quality)
                const scale = 2;
                tempCanvas.width = canvas.offsetWidth * scale;
                tempCanvas.height = canvas.offsetHeight * scale;
                tempCtx.scale(scale, scale);

                // Draw the canvas content
                const canvasRect = canvas.getBoundingClientRect();
                tempCtx.drawImage(canvas, 0, 0, canvas.offsetWidth, canvas.offsetHeight);

                // Get high-quality image data
                const imageDataURL = tempCanvas.toDataURL('image/png', 1.0);
                localStorage.setItem('billboardCanvasImage', imageDataURL);
                localStorage.setItem('adPreviewImage', imageDataURL);

                console.log('Canvas image captured for checkout:', imageDataURL.length, 'bytes');
            }
        } catch (error) {
            console.error('Error capturing canvas image:', error);
        }

        // Update dialog content
        CF7TextEditorCheckout.updateCheckoutDialogContent(requiredData);

        // Show the dialog using modal structure (same as background modal)
        const dialog = document.getElementById('checkoutDialog');
        if (dialog) {
            try {
                dialog.showModal();
                // Ensure proper centering
                dialog.style.top = '50%';
                dialog.style.left = '50%';
                dialog.style.transform = 'translate(-50%, -50%)';
                dialog.style.margin = 'auto';
                console.log('Checkout dialog displayed using modal structure');
            } catch (error) {
                // Fallback for browsers that don't support showModal
                dialog.style.display = 'flex';
                dialog.style.visibility = 'visible';
                dialog.style.opacity = '1';
                dialog.style.top = '50%';
                dialog.style.left = '50%';
                dialog.style.transform = 'translate(-50%, -50%)';
                dialog.style.margin = 'auto';
                document.body.style.overflow = 'hidden';
                console.log('Checkout dialog displayed using fallback method');
            }
        }
    }

    static updateCheckoutDialogContent(requiredData) {
        try {
            // Purpose field is hidden - no need to update it

            // Update location
            const locationElement = document.getElementById('dialogSummaryLocation');
            if (locationElement) {
                const fullLocation = requiredData.location || `${requiredData.state}, ${requiredData.city}`;
                locationElement.textContent = fullLocation;
            }

            // Update dates
            const datesElement = document.getElementById('dialogSummaryDates');
            if (datesElement) {
                datesElement.textContent = requiredData.dates || 'Not specified';
            }

            // Calculate duration
            const durationElement = document.getElementById('dialogSummaryDuration');
            if (durationElement) {
                const days = CF7TextEditorCheckout.calculateDays(requiredData.dates);
                durationElement.textContent = `${days} day${days !== 1 ? 's' : ''}`;
            }

            // Calculate cost (basic calculation - $50 per day)
            const costElement = document.getElementById('dialogSummaryCost');
            if (costElement) {
                const days = CF7TextEditorCheckout.calculateDays(requiredData.dates);
                const dailyRate = 50; // $50 per day
                const total = days * dailyRate;
                costElement.textContent = `$${total.toFixed(2)}`;

                // Store total cost for payment page
                localStorage.setItem('totalCost', total.toFixed(2));
            }

            console.log('Checkout dialog content updated');

        } catch (error) {
            console.error('Error updating checkout dialog content:', error);
        }
    }

    static calculateDays(dateRange) {
        if (!dateRange || dateRange === 'Not selected' || dateRange === 'Not specified') return 7;

        try {
            const dates = dateRange.split(' - ');
            if (dates.length === 2) {
                const startDate = new Date(dates[0]);
                const endDate = new Date(dates[1]);
                const timeDiff = endDate.getTime() - startDate.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates
                return Math.max(1, daysDiff); // Minimum 1 day
            }
        } catch (error) {
            console.error('Error calculating days:', error);
        }

        return 7; // Default to 7 days if calculation fails
    }

    static closeCheckoutDialog() {
        const dialog = document.getElementById('checkoutDialog');
        if (dialog) {
            try {
                dialog.close();
                console.log('Checkout dialog closed using modal structure');
            } catch (error) {
                // Fallback for browsers that don't support close
                dialog.style.display = 'none';
                dialog.style.visibility = 'hidden';
                dialog.style.opacity = '0';
                document.body.style.overflow = '';
                console.log('Checkout dialog closed using fallback method');
            }
        }
    }

    static proceedToPayment() {
        console.log('Proceeding to payment...');

        // Validate all required checkboxes are checked
        const requiredCheckboxes = [
            'dialog_terms_agreement',
            'dialog_content_compliance',
            'dialog_business_ad_compliance',
            'dialog_ad_preview_confirmation',
            'dialog_refund_policy_agreement'
        ];

        const missingCheckboxes = [];
        for (const checkboxId of requiredCheckboxes) {
            const checkbox = document.getElementById(checkboxId);
            if (!checkbox || !checkbox.checked) {
                missingCheckboxes.push(checkboxId.replace('dialog_', '').replace(/_/g, ' '));
            }
        }

        if (missingCheckboxes.length > 0) {
            alert(`Please check all required agreement boxes before proceeding:\n\n• ${missingCheckboxes.join('\n• ')}`);
            return;
        }

        // Store email copy preference
        const emailCopyCheckbox = document.getElementById('dialog_email_copy');
        localStorage.setItem('emailCopyRequested', emailCopyCheckbox ? emailCopyCheckbox.checked : false);

        // Show loading state
        const proceedButton = document.getElementById('checkout-modal-proceed');
        if (proceedButton) {
            proceedButton.textContent = 'Redirecting to Payment...';
            proceedButton.disabled = true;
        }

        // Close dialog
        CF7TextEditorCheckout.closeCheckoutDialog();

        // Redirect to payment page
        setTimeout(() => {
            window.location.href = 'https://www.borgesmedia.com/custom-billboard-ad-pay/';
        }, 500);
    }

    static openTermsModal() {
        // Simple terms modal - you can enhance this
        alert('Terms and Conditions:\n\n1. All billboard content must comply with local regulations\n2. No refunds for non-compliant content\n3. Design changes after approval incur additional fees\n4. Payment is required before billboard display\n5. Content must be appropriate for public display');
    }
}

// Make functions globally accessible
window.openCheckoutDialog = CF7TextEditorCheckout.openCheckoutDialog;
window.closeCheckoutDialog = CF7TextEditorCheckout.closeCheckoutDialog;
window.proceedToPayment = CF7TextEditorCheckout.proceedToPayment;
window.openTermsModal = CF7TextEditorCheckout.openTermsModal;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CF7TextEditorCheckout;
}